package gemini

import (
	"brainHub/internal/consts"
	"brainHub/internal/llms"
	"brainHub/internal/llms/common"
	"brainHub/internal/llms/mcp"
	"brainHub/internal/model/llm"
	"brainHub/utility"
	"context"
	"net/http"
	"path"
	"strings"
	"time"

	"cloud.google.com/go/auth"
	"cloud.google.com/go/auth/credentials"
	"github.com/gabriel-vasile/mimetype"
	"github.com/gogf/gf/v2/encoding/gjson"
	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gfile"
	"github.com/gogf/gf/v2/os/glog"
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/text/gstr"
	"github.com/gogf/gf/v2/util/gconv"
	"google.golang.org/genai"
)

type GeminiLLM struct {
	client          *genai.Client
	chat            *genai.Chat
	config          *genai.GenerateContentConfig
	modelName       string
	temperature     float32
	maxOutputTokens int32
	payload         *llm.Payload
	mcpToolManager  *mcp.MCPToolManager // MCP 工具管理器
}

func New() llms.ILLMs {
	return &GeminiLLM{}
}

func (m *GeminiLLM) logger() glog.ILogger {
	return g.Log().Cat(consts.CatalogGemini)
}

func (m *GeminiLLM) Initialize(ctx context.Context, params *llm.LLMsConfig, payload *llm.Payload) (err error) {

	m.logger().Debugf(ctx, "initiliaze with parameter  : %v", gjson.New(params).MustToJsonIndentString())
	if params == nil {
		m.logger().Error(ctx, "the params is nil")
		return gerror.New("the params is nil")
	}

	if m.client != nil && m.chat != nil && m.config != nil {
		m.logger().Info(ctx, "the client is already initialized")
		return
	}

	credentialFile := params.Vertex.CredentialFile

	if !path.IsAbs(params.Vertex.CredentialFile) {
		vPath, _ := g.Cfg().Get(ctx, "system.vertex_key_path", ".")
		credentialFile = gfile.Join(vPath.String(), params.Vertex.CredentialFile)

	}
	credentialFile = gfile.Abs(credentialFile)
	var creds *auth.Credentials
	if gfile.IsFile(credentialFile) && gfile.IsReadable(credentialFile) {
		jsonData := gfile.GetBytes(credentialFile)
		creds, err = credentials.DetectDefault(&credentials.DetectOptions{
			Scopes:          []string{"https://www.googleapis.com/auth/cloud-platform"},
			CredentialsJSON: jsonData,
		})

		if err != nil {
			m.logger().Error(ctx, err)
			return err
		}

	} else {
		err = gerror.New("the credential file is not exist")
		m.logger().Error(ctx, err)
		return
	}

	// 設置代理配置，環境需要透過 proxy 連接外部 API
	clientConfig := &genai.ClientConfig{
		Project:     params.Vertex.ProjectID,
		Location:    params.Vertex.Region,
		Backend:     genai.BackendVertexAI,
		Credentials: creds,
	}

	// 檢查是否需要設置代理
	//vProxy, _ := g.Cfg().Get(ctx, "system.proxy")
	//if vProxy != nil && !vProxy.IsEmpty() {
	//	proxyURL, parseErr := url.Parse(vProxy.String())
	//	if parseErr != nil {
	//		m.logger().Warningf(ctx, "Failed to parse proxy URL: %v", parseErr)
	//	} else {
	//		m.logger().Infof(ctx, "Setting up proxy for Gemini client: %s", vProxy.String())
	//
	//		// 創建帶代理的 HTTP 客戶端
	//		httpClient := &http.Client{
	//			Transport: &http.Transport{
	//				Proxy: http.ProxyURL(proxyURL),
	//			},
	//			Timeout: 60 * time.Second,
	//		}
	//
	//		// 設置 HTTP 客戶端到配置中
	//		clientConfig.HTTPClient = httpClient
	//	}
	//}

	m.client, err = genai.NewClient(ctx, clientConfig)

	if err != nil {
		err = gerror.WrapCode(consts.ApiFailed, err)
		m.logger().Error(ctx, err)
		return
	}

	m.config = &genai.GenerateContentConfig{
		Temperature:      genai.Ptr[float32](params.Vertex.Gemini.Temperature),
		MaxOutputTokens:  params.Vertex.Gemini.MaxOutputTokens,
		ResponseMIMEType: "application/json",
		ThinkingConfig: &genai.ThinkingConfig{
			IncludeThoughts: params.Vertex.Gemini.IncludeThoughts,
			ThinkingBudget:  genai.Ptr[int32](params.Vertex.Gemini.ThinkingBudget),
		},
	}

	m.temperature = params.Vertex.Gemini.Temperature
	m.maxOutputTokens = params.Vertex.Gemini.MaxOutputTokens
	m.modelName = params.Vertex.Gemini.Model
	m.payload = payload

	// 初始化 MCP 工具管理器
	configManager := mcp.NewConfigManager()
	mcpConfig, err := configManager.LoadMCPConfig(ctx)
	if err != nil {
		m.logger().Warningf(ctx, "Failed to load MCP config: %v", err)
		// 不阻止 LLM 初始化，繼續執行
	} else if mcpConfig != nil && mcpConfig.Enabled {
		m.mcpToolManager = mcp.NewMCPToolManager(mcpConfig)
		if err := m.mcpToolManager.Initialize(ctx); err != nil {
			m.logger().Errorf(ctx, "Failed to initialize MCP tool manager: %v", err)
			// 不阻止 LLM 初始化，只記錄錯誤
		} else {
			m.logger().Info(ctx, "MCP tool manager initialized successfully")
		}
	} else {
		m.logger().Debug(ctx, "MCP is disabled or not configured")
	}

	// todo for debug
	//m.chat, err = m.client.Chats.Create(ctx, m.modelName, m.config, nil)
	err = m.createNewChat(ctx, payload, nil)

	return
}

func (m *GeminiLLM) createNewChat(ctx context.Context, payload *llm.Payload, dialogSummary *genai.Content) (err error) {
	if len(gjson.New(payload).MustToJsonIndentString()) > 2000 {
		m.logger().Debugf(ctx, "create new chat : %v  ... ", gjson.New(payload).MustToJsonIndentString()[:2000])
	} else {
		m.logger().Debugf(ctx, "create new chat : %v  ... ", gjson.New(payload).MustToJsonIndentString())
	}
	systemInstruction := ""
	if payload != nil {
		systemInstruction = payload.SystemInstruction
	}

	// set system instruction
	systemInstruction = gstr.Replace(systemInstruction, "{{.now_date}}", gtime.Now().Format("Y-m-d"))

	if !g.IsEmpty(systemInstruction) {

		m.config.SystemInstruction = genai.NewContentFromText(systemInstruction, genai.RoleUser)
	}

	var contents []*genai.Content

	if payload != nil && payload.Attachments != nil {
		// 載爬文網站
		for _, webPageMDFile := range payload.Attachments.WebPageFiles {
			if gfile.Exists(webPageMDFile) && gfile.Size(webPageMDFile) > 0 {
				m.logger().Debugf(ctx, "Load webpage file:%v", webPageMDFile)
				buf := gfile.GetBytes(webPageMDFile)
				contents = append(contents, genai.NewContentFromBytes(buf, "text/md", genai.RoleUser))
			}

		}

		// files
		processedFiles := 0
		skippedFiles := 0
		for _, file := range payload.Attachments.Files {
			if gfile.Exists(file) && gfile.Size(file) > 0 {
				m.logger().Debugf(ctx, "Load file:%v", file)
				mType := ""
				if mimeType, e := mimetype.DetectFile(file); e != nil {
					m.logger().Error(ctx, e)
					skippedFiles++
					continue
				} else {
					mType = mimeType.String()
				}

				// 嘗試使用 markitdown 轉換文件
				convertedPath, isConverted, shouldSkip, convErr := utility.ConvertFileToMarkdown(ctx, file, mType)
				if convErr != nil || shouldSkip {
					if convErr != nil {
						m.logger().Errorf(ctx, "Failed to convert file %s to markdown, skipping file: %v", file, convErr)
					}
					skippedFiles++
					continue
				}

				// 使用轉換後的文件（或原始文件如果不需要轉換）
				finalMimeType := mType
				if isConverted {
					finalMimeType = "text/markdown"
					// 如果轉換成功，需要在處理完成後清理臨時文件
					defer func(path string) {
						if path != file { // 只清理轉換生成的文件
							_ = gfile.RemoveFile(path)
						}
					}(convertedPath)
				}
				contents = append(contents, genai.NewContentFromBytes(gfile.GetBytes(convertedPath), finalMimeType, genai.RoleUser))
				processedFiles++
			} else {
				skippedFiles++
			}
		}

		if len(payload.Attachments.Files) > 0 {
			m.logger().Infof(ctx, "File attachment processing completed: processed=%d, skipped=%d", processedFiles, skippedFiles)
		}
		// plain text
		for _, plainText := range payload.Attachments.PlainText {
			contents = append(contents, genai.NewContentFromText(plainText, genai.RoleUser))
		}

		// Youtube url

		if len(payload.Attachments.YoutubeLink) > 0 {
			ytLink := payload.Attachments.YoutubeLink[0]
			//mime := utility.GetYTMime(ctx, ytLink)
			mime := "video/*"
			contents = append(contents, genai.NewContentFromURI(ytLink, mime, genai.RoleUser))
		}

		if payload.History != nil {
			for _, message := range gconv.Strings(payload.History) {
				contents = append(contents, genai.NewContentFromText(message, genai.RoleUser))
			}
		}

	}
	// 添加前次對話的總結
	if dialogSummary != nil {
		contents = append(contents, dialogSummary)
	}

	m.chat, err = m.client.Chats.Create(ctx, m.modelName, m.config, contents)
	if err != nil {
		err = gerror.WrapCode(consts.ApiFailed, err)
		m.logger().Error(ctx, err)
		return
	}

	return
}

func (m *GeminiLLM) summarized(ctx context.Context) (content *genai.Content, err error) {
	history := m.chat.History(false)

	var parts []*genai.Part
	for _, content := range history {
		for _, part := range content.Parts {
			parts = append(parts, part)
		}
	}

	parts = append(parts, &genai.Part{Text: "Please use traditional Chinese to summarize the above dialogue content in a concise manner"})

	r, err := m.client.Models.GenerateContent(ctx, m.modelName, []*genai.Content{{Parts: parts, Role: genai.RoleUser}},
		&genai.GenerateContentConfig{
			MaxOutputTokens: m.maxOutputTokens,
			Temperature:     genai.Ptr[float32](m.temperature),
		})

	if err != nil {
		m.logger().Error(ctx, err)
		return nil, gerror.WrapCode(consts.ApiFailed, err)
	}

	if r != nil && len(r.Candidates) > 0 {
		summary := r.Candidates[0].Content.Parts[0].Text
		m.logger().Debugf(ctx, "summary: %v", summary)
		content = genai.NewContentFromText(summary, genai.RoleUser)
	} else {
		err = gerror.New("failed to generate summary")
		m.logger().Error(ctx, err)
	}

	return content, err
}

func (m *GeminiLLM) Chat(ctx context.Context, message *llm.Message) (response *llm.ResponseData, err error) {

	m.logger().Infof(ctx, "chat with message : %v", gjson.New(message).MustToJsonIndentString())
	//計算 tokens
	if m.chat != nil && m.payload != nil {
		if len(m.chat.History(false)) > 0 {
			historyRecords := m.chat.History(false)
			tokens, e := m.client.Models.CountTokens(ctx, m.modelName, historyRecords, nil)

			if e != nil {

				m.logger().Error(ctx, gerror.Cause(e))

			} else {
				if tokens != nil {
					m.logger().Debugf(ctx, "provide content tokens : %v", tokens.TotalTokens)
					if tokens.TotalTokens > consts.GeminiMaxTokens {
						content, _ := m.summarized(ctx)
						_ = m.createNewChat(ctx, m.payload, content)
					}
				}
			}

		}
	}

	var content *genai.Content
	switch message.ContentType {
	default:

		err = gerror.Newf("The content type %v is not supported  ", message.ContentType)
		m.logger().Error(ctx, err)
		return
	case consts.ContentMediaFile:

		data := gconv.Bytes(message.Content)
		mimeType := message.MimeType
		if g.IsEmpty(mimeType) {
			mimeType = http.DetectContentType(data)
		}
		m.logger().Debugf(ctx, "mime type: %s", mimeType)

		if m.chat.History(false) != nil && len(m.chat.History(false)) == 0 {
			content = &genai.Content{
				Parts: []*genai.Part{
					{
						InlineData: &genai.Blob{
							Data:     data,
							MIMEType: mimeType,
						},
					},
					{
						Text: "分析這個文件",
					},
				},
				Role: genai.RoleUser,
			}

		} else {

			content = genai.NewContentFromBytes(data, mimeType, genai.RoleUser)
		}

	case consts.ContentTypeText:
		content = genai.NewContentFromText(gconv.String(message.Content), genai.RoleUser)
	}

	// 支持工具調用的消息發送
	r, err := m.sendMessageWithTools(ctx, content)
	if err != nil {
		m.logger().Error(ctx, "Failed to send message with tools:", err)
		return nil, err
	}
	if r != nil {
		response = &llm.ResponseData{
			Response:        r.Candidates[0].Content.Parts[0].Text,
			TotalTokenCount: r.UsageMetadata.TotalTokenCount,
		}

	} else {
		err = gerror.New("the response is nil")
		m.logger().Error(ctx, err)
	}

	return
}

// GenerateContent 統一的內容生成接口，支援智能續寫和完整響應處理
// 實現跨模型一致的內容生成功能
func (m *GeminiLLM) GenerateContent(ctx context.Context, request *llm.GenerateContentRequest) (*llm.GenerateContentResponse, error) {
	startTime := gtime.TimestampMilli()

	// 參數驗證
	if request == nil {
		err := gerror.New("request cannot be nil")
		m.logger().Error(ctx, err)
		return nil, err
	}

	if g.IsEmpty(request.Prompt) {
		err := gerror.New("prompt cannot be empty")
		m.logger().Error(ctx, err)
		return nil, err
	}

	// 檢查客戶端是否已初始化
	if m.client == nil {
		err := gerror.New("gemini client is not initialized")
		m.logger().Error(ctx, err)
		return nil, err
	}

	// 應用預設值
	m.applyDefaults(ctx, request)

	// 記錄開始生成的日誌
	m.logger().Infof(ctx, "Starting GenerateContent: prompt_length=%d, max_continuations=%d, token_budget=%d",
		len(request.Prompt), request.MaxContinuations, request.TotalTokenBudget)

	// 初始化響應結構
	response := &llm.GenerateContentResponse{
		LLMName:           m.modelName,
		InputContent:      request.Prompt,
		ContinuationCount: 0,
		IsComplete:        false,
		SafetyWarnings:    make([]string, 0),
	}

	// 執行智能續寫生成
	err := m.executeGenerationWithContinuation(ctx, request, response)
	if err != nil {
		return nil, err
	}

	// 計算生成時間
	response.GenerationTime = gtime.TimestampMilli() - startTime

	// 記錄完成日誌
	m.logger().Infof(ctx, "GenerateContent completed: model=%s, continuations=%d/%d, input_tokens=%d, output_tokens=%d, total_tokens=%d, complete=%v, duration=%dms",
		response.LLMName, response.ContinuationCount, request.MaxContinuations,
		response.InputTokens, response.OutputTokens, response.TotalTokens,
		response.IsComplete, response.GenerationTime)

	return response, nil
}

// applyDefaults 應用預設配置值
func (m *GeminiLLM) applyDefaults(ctx context.Context, request *llm.GenerateContentRequest) {
	if request.MaxContinuations <= 0 {
		defaultValue, _ := g.Cfg().Get(ctx, "llms.gemini.max_continuations", 3)
		request.MaxContinuations = gconv.Int(defaultValue)
	}

	if request.TotalTokenBudget <= 0 {
		defaultValue, _ := g.Cfg().Get(ctx, "llms.gemini.total_token_budget", m.maxOutputTokens*2)
		request.TotalTokenBudget = gconv.Int32(defaultValue)
	}

	if request.Temperature == nil {
		request.Temperature = &m.temperature
	}
}

// executeGenerationWithContinuation 執行智能續寫生成
func (m *GeminiLLM) executeGenerationWithContinuation(ctx context.Context, request *llm.GenerateContentRequest, response *llm.GenerateContentResponse) error {
	var allContent strings.Builder
	var totalInputTokens, totalOutputTokens int32
	continuationCount := 0

	for continuationCount <= request.MaxContinuations {
		// 構建當前請求的 prompt
		currentPrompt := request.Prompt
		if continuationCount > 0 {
			currentPrompt = allContent.String() + "\n\n[請繼續完成上述內容]"
		}

		// 檢查 token 預算
		if totalInputTokens+totalOutputTokens >= request.TotalTokenBudget {
			m.logger().Debugf(ctx, "Token budget exceeded: %d/%d", totalInputTokens+totalOutputTokens, request.TotalTokenBudget)
			break
		}

		// 調用 Gemini API
		apiResponse, err := m.callGeminiAPI(ctx, currentPrompt, request)
		if err != nil {
			return err
		}

		// 處理響應
		processedResponse, err := m.processGeminiResponse(apiResponse, request.IncludeThinking)
		if err != nil {
			return err
		}

		// 累加內容和 token
		allContent.WriteString(processedResponse.OutputContent)
		totalInputTokens += processedResponse.InputTokens
		totalOutputTokens += processedResponse.OutputTokens

		// 合併安全警告
		response.SafetyWarnings = append(response.SafetyWarnings, processedResponse.SafetyWarnings...)

		// 保存思考過程（如果有）
		if request.IncludeThinking && !g.IsEmpty(processedResponse.ThinkingProcess) {
			if g.IsEmpty(response.ThinkingProcess) {
				response.ThinkingProcess = processedResponse.ThinkingProcess
			} else {
				response.ThinkingProcess += "\n\n" + processedResponse.ThinkingProcess
			}
		}

		// 保存完成原因
		response.FinishReason = processedResponse.FinishReason

		// 檢測是否需要續寫
		isComplete, reason := m.isContentComplete(allContent.String(), request.IncludeThinking)

		m.logger().Debugf(ctx, "Continuation %d: reason=%s, tokens_used=%d/%d, complete=%v",
			continuationCount, reason, totalInputTokens+totalOutputTokens, request.TotalTokenBudget, isComplete)

		if isComplete {
			response.IsComplete = true
			break
		}

		continuationCount++
	}

	// 設置最終響應數據
	response.OutputContent = allContent.String()
	response.InputTokens = totalInputTokens
	response.OutputTokens = totalOutputTokens
	response.TotalTokens = totalInputTokens + totalOutputTokens
	response.ContinuationCount = continuationCount

	return nil
}

// isContentComplete 檢測內容是否完整
func (m *GeminiLLM) isContentComplete(content string, includeThinking bool) (bool, string) {
	// 檢測未完成的程式碼區塊
	if gstr.Count(content, "```")%2 != 0 {
		return false, "incomplete_code_block"
	}

	// 檢測思考過程是否完整（如果啟用）
	if includeThinking && gstr.Contains(content, "<thinking>") && !gstr.Contains(content, "</thinking>") {
		return false, "incomplete_thinking"
	}

	// 檢測句子是否在合適位置結束
	trimmed := gstr.Trim(content)
	if g.IsEmpty(trimmed) {
		return false, "empty_content"
	}

	// 檢測常見的未完成標記
	incompletePatterns := []string{"...", "待續", "to be continued", "（未完）", "未完待續"}
	for _, pattern := range incompletePatterns {
		if gstr.ContainsI(content, pattern) {
			return false, "explicit_incomplete_marker"
		}
	}

	return true, "complete"
}

// callGeminiAPI 調用 Gemini API
func (m *GeminiLLM) callGeminiAPI(ctx context.Context, prompt string, request *llm.GenerateContentRequest) (*genai.GenerateContentResponse, error) {
	// 創建內容
	content := &genai.Content{
		Parts: []*genai.Part{
			{Text: prompt},
		},
		Role: genai.RoleUser,
	}

	// 創建生成配置
	config := &genai.GenerateContentConfig{
		Temperature:     request.Temperature,
		MaxOutputTokens: m.maxOutputTokens,
	}

	// 添加系統指令（如果有）
	if !g.IsEmpty(request.SystemInstruction) {
		config.SystemInstruction = genai.NewContentFromText(request.SystemInstruction, genai.RoleUser)
	}

	// 添加思考配置（如果啟用）
	if request.IncludeThinking {
		config.ThinkingConfig = &genai.ThinkingConfig{
			IncludeThoughts: true,
			ThinkingBudget:  genai.Ptr[int32](1000), // 預設思考預算
		}
	}

	var r *genai.GenerateContentResponse
	var err error

	// 重試邏輯，與 Chat 方法保持一致
	for i := 0; i < 3; i++ {
		r, err = m.client.Models.GenerateContent(ctx, m.modelName, []*genai.Content{content}, config)

		if err != nil {
			m.logger().Error(ctx, err)
			// 使用配置中的重試延遲時間
			vTtl, _ := g.Cfg().Get(ctx, "system.ai_send_retry_ttl", "40s")
			time.Sleep(vTtl.Duration())
		} else {
			break
		}
	}

	if err != nil {
		return nil, gerror.WrapCode(consts.ApiFailed, err, "failed to call Gemini API after retries")
	}

	return r, nil
}

// processGeminiResponse 處理 Gemini API 的完整響應
func (m *GeminiLLM) processGeminiResponse(r *genai.GenerateContentResponse, includeThinking bool) (*llm.GenerateContentResponse, error) {
	if r == nil || len(r.Candidates) == 0 {
		return nil, gerror.New("empty response from Gemini API")
	}

	candidate := r.Candidates[0]
	response := &llm.GenerateContentResponse{
		LLMName:        m.modelName,
		SafetyWarnings: make([]string, 0),
	}

	// 處理完成原因
	if candidate.FinishReason != "" {
		response.FinishReason = string(candidate.FinishReason)
	}

	// 處理安全評級
	for _, rating := range candidate.SafetyRatings {
		if rating.Probability != genai.HarmProbabilityNegligible {
			response.SafetyWarnings = append(response.SafetyWarnings,
				gstr.Join([]string{string(rating.Category), string(rating.Probability)}, ": "))
		}
	}

	// 提取內容
	var contentBuilder strings.Builder
	var thinkingBuilder strings.Builder

	for _, part := range candidate.Content.Parts {
		if !g.IsEmpty(part.Text) {
			// 區分思考過程和正常內容
			if includeThinking && gstr.Contains(part.Text, "<thinking>") {
				thinkingBuilder.WriteString(part.Text)
			} else {
				contentBuilder.WriteString(part.Text)
			}
		}

		// 處理函數調用（如果有）
		if part.FunctionCall != nil {
			contentBuilder.WriteString(gstr.Join([]string{"[Function Call: ", part.FunctionCall.Name, "]"}, ""))
		}
	}

	response.OutputContent = contentBuilder.String()
	if includeThinking {
		response.ThinkingProcess = thinkingBuilder.String()
	}

	// 處理 token 統計
	if r.UsageMetadata != nil {
		response.InputTokens = r.UsageMetadata.PromptTokenCount
		response.OutputTokens = r.UsageMetadata.CandidatesTokenCount
		response.TotalTokens = r.UsageMetadata.TotalTokenCount
	}

	return response, nil
}

// GenerateContentSimple 簡化版本，向後兼容
func (m *GeminiLLM) GenerateContentSimple(ctx context.Context, prompt string) (string, error) {
	request := &llm.GenerateContentRequest{
		Prompt:           prompt,
		MaxContinuations: 3,
		TotalTokenBudget: m.maxOutputTokens * 2,
		IncludeThinking:  false,
	}

	response, err := m.GenerateContent(ctx, request)
	if err != nil {
		return "", err
	}

	return response.OutputContent, nil
}

func (m *GeminiLLM) Release(ctx context.Context) {

	//_ = m.createNewChat(ctx)
}

// sendMessageWithTools 支持工具調用的消息發送
func (m *GeminiLLM) sendMessageWithTools(ctx context.Context, content *genai.Content) (*genai.GenerateContentResponse, error) {
	// 獲取工具定義
	var toolDefinitions []common.ToolDefinition
	if m.mcpToolManager != nil {
		tools, err := m.mcpToolManager.GetToolDefinitions(ctx)
		if err != nil {
			m.logger().Error(ctx, "Failed to get tool definitions:", err)
		} else {
			toolDefinitions = tools
			m.logger().Debugf(ctx, "Loaded %d tool definitions for Gemini", len(tools))
		}
	}

	// 準備消息部分
	var parts []genai.Part
	for _, part := range content.Parts {
		parts = append(parts, *part)
	}

	// 如果有工具定義，設置工具配置
	if len(toolDefinitions) > 0 {
		tools := m.convertToGeminiTools(toolDefinitions)
		m.config.Tools = tools
	}

	// 發送消息並處理工具調用
	return m.processGeminiWithTools(ctx, parts, toolDefinitions)
}

// convertToGeminiTools 轉換工具定義為 Gemini 格式
func (m *GeminiLLM) convertToGeminiTools(toolDefinitions []common.ToolDefinition) []*genai.Tool {
	var tools []*genai.Tool

	for _, toolDef := range toolDefinitions {
		// 轉換參數格式
		var schema *genai.Schema
		if toolDef.Parameters != nil {
			schema = &genai.Schema{
				Type:       genai.TypeObject,
				Properties: make(map[string]*genai.Schema),
			}

			// 轉換參數屬性
			if properties, ok := toolDef.Parameters["properties"].(map[string]interface{}); ok {
				for propName, propInfo := range properties {
					if propMap, ok := propInfo.(map[string]interface{}); ok {
						propSchema := &genai.Schema{}
						if propType, ok := propMap["type"].(string); ok {
							switch propType {
							case "string":
								propSchema.Type = genai.TypeString
							case "number":
								propSchema.Type = genai.TypeNumber
							case "integer":
								propSchema.Type = genai.TypeInteger
							case "boolean":
								propSchema.Type = genai.TypeBoolean
							case "array":
								propSchema.Type = genai.TypeArray
							case "object":
								propSchema.Type = genai.TypeObject
							}
						}
						if desc, ok := propMap["description"].(string); ok {
							propSchema.Description = desc
						}
						schema.Properties[propName] = propSchema
					}
				}
			}

			// 設置必需字段
			if required, ok := toolDef.Parameters["required"].([]interface{}); ok {
				for _, req := range required {
					if reqStr, ok := req.(string); ok {
						schema.Required = append(schema.Required, reqStr)
					}
				}
			}
		}

		tool := &genai.Tool{
			FunctionDeclarations: []*genai.FunctionDeclaration{
				{
					Name:        toolDef.Name,
					Description: toolDef.Description,
					Parameters:  schema,
				},
			},
		}
		tools = append(tools, tool)
	}

	return tools
}

// processGeminiWithTools 處理 Gemini 的工具調用
func (m *GeminiLLM) processGeminiWithTools(ctx context.Context, parts []genai.Part, toolDefinitions []common.ToolDefinition) (*genai.GenerateContentResponse, error) {
	var r *genai.GenerateContentResponse
	var err error

	// 重試機制
	for i := 0; i < 3; i++ {
		r, err = m.chat.SendMessage(ctx, parts...)

		if err != nil {
			m.logger().Error(ctx, err)
			vTtl, _ := g.Cfg().Get(ctx, "system.ai_send_retry_ttl", "40s")
			time.Sleep(vTtl.Duration())
		} else {
			break
		}
	}

	if err != nil {
		return nil, err
	}

	if r == nil {
		return nil, gerror.New("the response is nil")
	}

	// 檢查是否有工具調用
	return m.handleGeminiToolCalls(ctx, r, toolDefinitions)
}

// handleGeminiToolCalls 處理 Gemini 工具調用
func (m *GeminiLLM) handleGeminiToolCalls(ctx context.Context, r *genai.GenerateContentResponse, toolDefinitions []common.ToolDefinition) (*genai.GenerateContentResponse, error) {
	if len(r.Candidates) == 0 {
		return r, nil
	}

	candidate := r.Candidates[0]
	if candidate.Content == nil || len(candidate.Content.Parts) == 0 {
		return r, nil
	}

	// 檢查是否有工具調用
	hasToolCalls := false
	for _, part := range candidate.Content.Parts {
		if part.FunctionCall != nil {
			hasToolCalls = true
			break
		}
	}

	if !hasToolCalls {
		// 沒有工具調用，直接返回
		return r, nil
	}

	m.logger().Infof(ctx, "Processing tool calls in Gemini response")

	// 執行工具調用
	var toolResponseParts []genai.Part
	for _, part := range candidate.Content.Parts {
		if part.FunctionCall != nil {
			result, err := m.executeGeminiToolCall(ctx, part.FunctionCall)
			if err != nil {
				m.logger().Errorf(ctx, "Tool call failed: %v", err)
			}

			// 創建工具響應
			toolResponse := genai.Part{
				FunctionResponse: &genai.FunctionResponse{
					Name: part.FunctionCall.Name,
					Response: map[string]interface{}{
						"success": result.Success,
						"content": result.Content,
						"error":   result.Error,
					},
				},
			}
			toolResponseParts = append(toolResponseParts, toolResponse)
		}
	}

	// 發送工具響應並獲取最終回應
	if len(toolResponseParts) > 0 {
		return m.sendToolResponseAndGetFinalResponse(ctx, toolResponseParts, toolDefinitions)
	}

	return r, nil
}

// executeGeminiToolCall 執行 Gemini 工具調用
func (m *GeminiLLM) executeGeminiToolCall(ctx context.Context, functionCall *genai.FunctionCall) (*common.ToolResult, error) {
	m.logger().Infof(ctx, "Executing Gemini tool: %s with args: %v",
		functionCall.Name, functionCall.Args)

	if m.mcpToolManager == nil {
		return &common.ToolResult{
			Success: false,
			Error:   "MCP tool manager not initialized",
		}, nil
	}

	// 調用工具
	return m.mcpToolManager.CallTool(ctx, functionCall.Name, functionCall.Args)
}

// sendToolResponseAndGetFinalResponse 發送工具響應並獲取最終回應
func (m *GeminiLLM) sendToolResponseAndGetFinalResponse(ctx context.Context, toolResponseParts []genai.Part, toolDefinitions []common.ToolDefinition) (*genai.GenerateContentResponse, error) {
	// 發送工具響應
	var r *genai.GenerateContentResponse
	var err error

	for i := 0; i < 3; i++ {
		r, err = m.chat.SendMessage(ctx, toolResponseParts...)

		if err != nil {
			m.logger().Error(ctx, err)
			vTtl, _ := g.Cfg().Get(ctx, "system.ai_send_retry_ttl", "40s")
			time.Sleep(vTtl.Duration())
		} else {
			break
		}
	}

	if err != nil {
		return nil, err
	}

	if r == nil {
		return nil, gerror.New("the response is nil")
	}

	// 遞歸處理（支持多輪工具調用）
	return m.handleGeminiToolCalls(ctx, r, toolDefinitions)
}
